"use client";
import React from 'react';

interface ColorPalette {
  id: string;
  name: string;
  description: string;
  colors: string[];
  category: 'neutral' | 'warm' | 'cool' | 'bold' | 'natural';
}

interface ColorPaletteSelectorProps {
  selectedPalette: string | null;
  onPaletteSelect: (paletteId: string) => void;
  className?: string;
}

const COLOR_PALETTES: ColorPalette[] = [
  // Neutral Palettes
  {
    id: 'soft-neutrals',
    name: 'Soft Neutrals',
    description: 'Calming beiges, creams, and soft grays',
    colors: ['#F5F5DC', '#E6E6FA', '#F0F8FF', '#FFF8DC', '#F5F5F5'],
    category: 'neutral'
  },
  {
    id: 'modern-grays',
    name: 'Modern Grays',
    description: 'Contemporary charcoal, silver, and white tones',
    colors: ['#2F2F2F', '#696969', '#A9A9A9', '#D3D3D3', '#F8F8FF'],
    category: 'neutral'
  },
  {
    id: 'warm-whites',
    name: 'Warm Whites',
    description: 'Ivory, cream, and off-white variations',
    colors: ['#FFFDD0', '#FDF5E6', '#FAF0E6', '#LINEN', '#SEASHELL'],
    category: 'neutral'
  },

  // Warm Palettes
  {
    id: 'earthy-warmth',
    name: 'Earthy Warmth',
    description: 'Rich terracotta, warm browns, and golden tones',
    colors: ['#CD853F', '#D2691E', '#F4A460', '#DEB887', '#F5DEB3'],
    category: 'warm'
  },
  {
    id: 'sunset-glow',
    name: 'Sunset Glow',
    description: 'Coral, peach, and warm orange hues',
    colors: ['#FF7F50', '#FFA07A', '#FFB6C1', '#FFDAB9', '#FFFFE0'],
    category: 'warm'
  },
  {
    id: 'autumn-spice',
    name: 'Autumn Spice',
    description: 'Deep burgundy, rust, and golden yellow',
    colors: ['#8B0000', '#B22222', '#CD853F', '#DAA520', '#F0E68C'],
    category: 'warm'
  },

  // Cool Palettes
  {
    id: 'coastal-calm',
    name: 'Coastal Calm',
    description: 'Ocean blues, seafoam, and sandy beiges',
    colors: ['#4682B4', '#87CEEB', '#B0E0E6', '#F0F8FF', '#F5F5DC'],
    category: 'cool'
  },
  {
    id: 'nordic-lights',
    name: 'Nordic Lights',
    description: 'Cool blues, icy grays, and crisp whites',
    colors: ['#4169E1', '#6495ED', '#B0C4DE', '#E6E6FA', '#F0F8FF'],
    category: 'cool'
  },
  {
    id: 'forest-retreat',
    name: 'Forest Retreat',
    description: 'Deep greens, sage, and natural wood tones',
    colors: ['#228B22', '#32CD32', '#90EE90', '#F0FFF0', '#DEB887'],
    category: 'cool'
  },

  // Bold Palettes
  {
    id: 'jewel-tones',
    name: 'Jewel Tones',
    description: 'Rich emerald, sapphire, and amethyst',
    colors: ['#50C878', '#0F52BA', '#9966CC', '#FFD700', '#2F2F2F'],
    category: 'bold'
  },
  {
    id: 'vibrant-energy',
    name: 'Vibrant Energy',
    description: 'Electric blues, bright corals, and sunny yellows',
    colors: ['#00BFFF', '#FF6347', '#FFD700', '#32CD32', '#FF1493'],
    category: 'bold'
  },
  {
    id: 'dramatic-contrast',
    name: 'Dramatic Contrast',
    description: 'Bold black, white, and accent colors',
    colors: ['#000000', '#FFFFFF', '#FF0000', '#FFD700', '#4169E1'],
    category: 'bold'
  },

  // Natural Palettes
  {
    id: 'botanical-garden',
    name: 'Botanical Garden',
    description: 'Fresh greens, earth tones, and floral accents',
    colors: ['#228B22', '#8FBC8F', '#DEB887', '#F5DEB3', '#FFB6C1'],
    category: 'natural'
  },
  {
    id: 'desert-sunset',
    name: 'Desert Sunset',
    description: 'Warm sands, cactus greens, and sunset pinks',
    colors: ['#F4A460', '#CD853F', '#9ACD32', '#FF69B4', '#FFE4E1'],
    category: 'natural'
  },
  {
    id: 'mountain-mist',
    name: 'Mountain Mist',
    description: 'Stone grays, misty blues, and alpine greens',
    colors: ['#708090', '#B0C4DE', '#2E8B57', '#F5F5DC', '#FFFAFA'],
    category: 'natural'
  }
];

const CATEGORY_LABELS = {
  neutral: 'Neutral',
  warm: 'Warm',
  cool: 'Cool', 
  bold: 'Bold',
  natural: 'Natural'
};

export const ColorPaletteSelector: React.FC<ColorPaletteSelectorProps> = ({
  selectedPalette,
  onPaletteSelect,
  className = ""
}) => {
  const [selectedCategory, setSelectedCategory] = React.useState<string>('all');

  const filteredPalettes = selectedCategory === 'all' 
    ? COLOR_PALETTES 
    : COLOR_PALETTES.filter(palette => palette.category === selectedCategory);

  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">Choose Color Palette</h3>
        <p className="text-gray-400 text-sm">
          Select a color scheme that matches your style preference
        </p>
      </div>

      {/* Category Filter */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all border ${
              selectedCategory === 'all'
                ? 'bg-teal-600 text-white border-teal-400'
                : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
            }`}
          >
            All
          </button>
          {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
            <button
              key={key}
              onClick={() => setSelectedCategory(key)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all border ${
                selectedCategory === key
                  ? 'bg-teal-600 text-white border-teal-400'
                  : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Palettes Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredPalettes.map((palette) => {
          const isSelected = selectedPalette === palette.id;
          
          return (
            <button
              key={palette.id}
              onClick={() => onPaletteSelect(palette.id)}
              className={`
                group flex flex-col p-4 rounded-xl border-2 transition-all duration-200 relative text-left
                ${isSelected 
                  ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40' 
                  : 'border-gray-700 bg-gray-900 hover:border-teal-500 hover:bg-gray-800 active:scale-95'
                }
              `}
            >
              {/* Color Swatches */}
              <div className="flex gap-1 mb-3">
                {palette.colors.map((color, index) => (
                  <div
                    key={index}
                    className="w-8 h-8 rounded-lg border border-gray-600 flex-1"
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>

              {/* Palette Info */}
              <div>
                <h4 className={`font-medium mb-1 ${
                  isSelected ? 'text-teal-400' : 'text-white'
                }`}>
                  {palette.name}
                </h4>
                <p className="text-gray-400 text-xs leading-relaxed">
                  {palette.description}
                </p>
              </div>

              {/* Category Badge */}
              <div className="mt-3">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  isSelected 
                    ? 'bg-teal-500/20 text-teal-300' 
                    : 'bg-gray-700 text-gray-400'
                }`}>
                  {CATEGORY_LABELS[palette.category]}
                </span>
              </div>

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Selected Palette Info */}
      {selectedPalette && (
        <div className="mt-6 p-4 bg-gray-800/30 rounded-xl border border-gray-700">
          {(() => {
            const selected = COLOR_PALETTES.find(p => p.id === selectedPalette);
            if (!selected) return null;
            
            return (
              <div>
                <div className="flex items-center gap-4 mb-3">
                  <div className="flex gap-1">
                    {selected.colors.map((color, index) => (
                      <div
                        key={index}
                        className="w-6 h-6 rounded border border-gray-600"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{selected.name}</h4>
                    <p className="text-gray-400 text-sm">{selected.description}</p>
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default ColorPaletteSelector;
export { COLOR_PALETTES, CATEGORY_LABELS };
export type { ColorPalette };
