"use client";
import React, { useState } from 'react';
import RoomTypeSelector, { ROOM_TYPES } from '../../components/RoomTypeSelector';

export default function RoomTypesDemoPage() {
  const [selectedRoomType, setSelectedRoomType] = useState<string | null>(null);

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Room Types Demo
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Explore all {ROOM_TYPES.length} room types with Material UI style icons and glowing hover effects.
            Each icon is custom-designed and features smooth animations.
          </p>
        </div>

        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
          <RoomTypeSelector
            selectedRoomType={selectedRoomType}
            onRoomTypeSelect={setSelectedRoomType}
          />
        </div>

        {/* Room Types List */}
        <div className="mt-12 bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
          <h2 className="text-2xl font-bold text-white mb-6">All Room Types ({ROOM_TYPES.length})</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {ROOM_TYPES.map((roomType) => (
              <div key={roomType.id} className="bg-gray-800/30 rounded-lg p-4 border border-gray-700">
                <div className="flex items-center gap-3 mb-2">
                  <div className="text-teal-400 w-6 h-6 flex items-center justify-center">
                    {roomType.icon}
                  </div>
                  <h3 className="text-white font-medium">{roomType.name}</h3>
                </div>
                <p className="text-gray-400 text-sm">{roomType.description}</p>
                <p className="text-gray-500 text-xs mt-2">ID: {roomType.id}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Features */}
        <div className="mt-12 bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
          <h2 className="text-2xl font-bold text-white mb-6">Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-teal-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Material UI Icons</h3>
              <p className="text-gray-400 text-sm">Custom SVG icons designed in Material UI style</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-teal-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Glowing Hover Effects</h3>
              <p className="text-gray-400 text-sm">Icons glow with teal light on hover</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-teal-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">26 Room Types</h3>
              <p className="text-gray-400 text-sm">Comprehensive coverage of all room types</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-teal-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Responsive Design</h3>
              <p className="text-gray-400 text-sm">Adapts to all screen sizes perfectly</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-12 text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/design/new"
              className="bg-teal-600 hover:bg-teal-500 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              Try the Full Design Wizard
            </a>
            <a
              href="/components-demo"
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              View All Components
            </a>
            <a
              href="/"
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              Back to Home
            </a>
          </div>
        </div>
      </div>
    </main>
  );
}
