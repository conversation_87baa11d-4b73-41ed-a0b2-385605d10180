"use client";
import React from 'react';

interface Style {
  id: number;
  label: string;
  image: string;
  category: 'modern' | 'traditional' | 'eclectic' | 'minimalist' | 'rustic' | 'luxury' | 'eco' | 'cultural';
  description?: string;
}

interface StyleSelectorProps {
  selectedStyles: number[];
  onStyleToggle: (styleId: number) => void;
  maxSelection?: number;
  className?: string;
}

const STYLES: Style[] = [
  // Modern Styles
  { id: 1, label: "Minimalist", image: "https://ext.same-assets.com/4123950039/1267053207.jpeg", category: 'minimalist', description: 'Clean lines, neutral colors, uncluttered spaces' },
  { id: 2, label: "Scandinavian", image: "https://ext.same-assets.com/4123950039/4031576135.jpeg", category: 'modern', description: 'Light woods, cozy textures, functional design' },
  { id: 3, label: "Industrial", image: "https://ext.same-assets.com/4123950039/2311013961.jpeg", category: 'modern', description: 'Raw materials, exposed elements, urban aesthetic' },
  { id: 4, label: "Mid-Century Modern", image: "https://ext.same-assets.com/4123950039/4247774748.jpeg", category: 'modern', description: 'Retro furniture, bold colors, geometric patterns' },
  { id: 10, label: "Modern", image: "https://ext.same-assets.com/4123950039/1390157197.jpeg", category: 'modern', description: 'Sleek lines, contemporary materials, functional design' },
  { id: 14, label: "Contemporary", image: "https://ext.same-assets.com/4123950039/3246906598.jpeg", category: 'modern', description: 'Current trends, clean lines, open spaces' },
  { id: 23, label: "Futuristic", image: "https://ext.same-assets.com/4123950039/1432023951.jpeg", category: 'modern', description: 'High-tech materials, sleek surfaces, space-age design' },

  // Traditional Styles
  { id: 9, label: "Traditional", image: "https://ext.same-assets.com/4123950039/362001557.jpeg", category: 'traditional', description: 'Classic furniture, rich fabrics, timeless elegance' },
  { id: 16, label: "French Country", image: "https://ext.same-assets.com/4123950039/690654409.jpeg", category: 'traditional', description: 'Romantic charm, soft colors, vintage French elements' },
  { id: 20, label: "Victorian", image: "https://ext.same-assets.com/4123950039/180188305.jpeg", category: 'traditional', description: 'Ornate details, rich fabrics, historical elegance' },
  { id: 11, label: "Transitional", image: "https://ext.same-assets.com/4123950039/891312444.jpeg", category: 'traditional', description: 'Blend of traditional and contemporary elements' },
  { id: 12, label: "Shabby Chic", image: "https://ext.same-assets.com/4123950039/1011160405.jpeg", category: 'traditional', description: 'Distressed furniture, soft pastels, vintage romance' },

  // Eclectic Styles
  { id: 5, label: "Bohemian", image: "https://ext.same-assets.com/4123950039/2162116070.jpeg", category: 'eclectic', description: 'Eclectic mix, rich textures, global influences' },
  { id: 6, label: "Art Deco", image: "https://ext.same-assets.com/4123950039/3217488218.jpeg", category: 'eclectic', description: 'Geometric patterns, luxurious materials, bold colors' },
  { id: 24, label: "Maximalist", image: "https://ext.same-assets.com/4123950039/1389072092.jpeg", category: 'eclectic', description: 'Bold patterns, rich colors, eclectic collections' },
  { id: 25, label: "Pop Art", image: "https://ext.same-assets.com/4123950039/3540851178.jpeg", category: 'eclectic', description: 'Bright colors, graphic elements, playful design' },
  { id: 26, label: "Moroccan", image: "https://ext.same-assets.com/4123950039/2521446536.jpeg", category: 'eclectic', description: 'Rich patterns, vibrant colors, exotic textures' },
  { id: 27, label: "Cosmic Chic", image: "https://ext.same-assets.com/4123950039/3420677188.jpeg", category: 'eclectic', description: 'Celestial themes, metallic accents, dreamy atmosphere' },

  // Rustic Styles
  { id: 7, label: "Coastal", image: "https://ext.same-assets.com/4123950039/4253859114.jpeg", category: 'rustic', description: 'Light colors, natural textures, beach-inspired elements' },
  { id: 8, label: "Farmhouse", image: "https://ext.same-assets.com/4123950039/2462314040.jpeg", category: 'rustic', description: 'Rustic charm, vintage elements, cozy comfort' },
  { id: 18, label: "Rustic", image: "https://ext.same-assets.com/4123950039/1650356877.jpeg", category: 'rustic', description: 'Natural materials, earthy tones, countryside charm' },
  { id: 19, label: "Southwestern", image: "https://ext.same-assets.com/4123950039/2422832776.jpeg", category: 'rustic', description: 'Desert colors, Native American influences, warm textures' },
  { id: 29, label: "Tropical", image: "https://ext.same-assets.com/4123950039/2401809942.jpeg", category: 'rustic', description: 'Lush greens, natural textures, resort-like atmosphere' },

  // Minimalist/Zen Styles
  { id: 13, label: "Japanese Zen", image: "https://ext.same-assets.com/4123950039/3933773814.jpeg", category: 'minimalist', description: 'Simplicity, natural materials, peaceful harmony' },
  { id: 28, label: "Wabi-Sabi", image: "https://ext.same-assets.com/4123950039/744976908.jpeg", category: 'minimalist', description: 'Imperfect beauty, natural aging, mindful simplicity' },

  // Luxury Styles
  { id: 15, label: "Gothic", image: "https://ext.same-assets.com/4123950039/1038660028.jpeg", category: 'luxury', description: 'Dark colors, dramatic elements, medieval inspiration' },
  { id: 17, label: "Hollywood Regency", image: "https://ext.same-assets.com/4123950039/927860819.jpeg", category: 'luxury', description: 'Glamorous luxury, bold patterns, metallic accents' },
  { id: 30, label: "Vintage Glam", image: "https://ext.same-assets.com/4123950039/1246017528.jpeg", category: 'luxury', description: 'Retro luxury, metallic finishes, sophisticated charm' },

  // Unique/Themed Styles
  { id: 21, label: "Steampunk", image: "https://ext.same-assets.com/4123950039/650580268.jpeg", category: 'eclectic', description: 'Industrial vintage, brass accents, mechanical elements' },
  { id: 31, label: "Cyberpunk", image: "https://ext.same-assets.com/4123950039/2253538115.jpeg", category: 'eclectic', description: 'Neon colors, high-tech elements, futuristic edge' },
  { id: 32, label: "Psychedelic", image: "https://ext.same-assets.com/4123950039/3683077406.jpeg", category: 'eclectic', description: 'Vibrant patterns, bold colors, mind-bending design' },
  { id: 33, label: "Surrealist", image: "https://ext.same-assets.com/4123950039/1098851357.jpeg", category: 'eclectic', description: 'Dreamlike elements, unexpected combinations, artistic flair' },
  { id: 34, label: "Post-Apocalyptic", image: "https://ext.same-assets.com/4123950039/576171152.jpeg", category: 'eclectic', description: 'Distressed materials, survival aesthetic, raw textures' },
  { id: 35, label: "Candy Land", image: "https://ext.same-assets.com/4123950039/3193769849.jpeg", category: 'eclectic', description: 'Sweet colors, playful elements, whimsical design' },

  // Eco & Nature Styles
  { id: 22, label: "Biophilic", image: "https://ext.same-assets.com/4123950039/3597336419.jpeg", category: 'eco', description: 'Nature integration, living plants, organic materials' },
  { id: 36, label: "Eco-Design", image: "https://ext.same-assets.com/4123950039/3597336419.jpeg", category: 'eco', description: 'Sustainable materials, environmental consciousness, green living' },
  { id: 37, label: "Living Walls", image: "https://ext.same-assets.com/4123950039/2401809942.jpeg", category: 'eco', description: 'Vertical gardens, green walls, natural air purification' },
  { id: 38, label: "Sustainable Modern", image: "https://ext.same-assets.com/4123950039/1267053207.jpeg", category: 'eco', description: 'Modern aesthetics with eco-friendly materials and principles' },
  { id: 39, label: "Green Minimalism", image: "https://ext.same-assets.com/4123950039/3933773814.jpeg", category: 'eco', description: 'Minimalist design enhanced with plants and natural elements' },
  { id: 40, label: "Urban Jungle", image: "https://ext.same-assets.com/4123950039/2162116070.jpeg", category: 'eco', description: 'Lush indoor plant environments, jungle-like atmosphere' },
  { id: 41, label: "Natural Materials", image: "https://ext.same-assets.com/4123950039/1650356877.jpeg", category: 'eco', description: 'Raw, unprocessed materials like stone, wood, bamboo' },
  { id: 42, label: "Earth Tones", image: "https://ext.same-assets.com/4123950039/2422832776.jpeg", category: 'eco', description: 'Nature-inspired colors: browns, greens, terracotta, warm neutrals' },
  { id: 43, label: "Botanical Garden", image: "https://ext.same-assets.com/4123950039/4253859114.jpeg", category: 'eco', description: 'Indoor botanical garden aesthetics with diverse plant species' },
  { id: 44, label: "Reclaimed Wood", image: "https://ext.same-assets.com/4123950039/2462314040.jpeg", category: 'eco', description: 'Recycled and reclaimed wood furniture, environmentally conscious' },
  { id: 45, label: "Solar Punk", image: "https://ext.same-assets.com/4123950039/1432023951.jpeg", category: 'eco', description: 'Futuristic eco-design with renewable energy and lush greenery' },

  // Cultural Styles
  { id: 46, label: "Mediterranean", image: "https://ext.same-assets.com/4123950039/4253859114.jpeg", category: 'cultural', description: 'Warm colors, natural stone, coastal European charm' },
  { id: 47, label: "Asian Fusion", image: "https://ext.same-assets.com/4123950039/3933773814.jpeg", category: 'cultural', description: 'Eastern influences, balanced elements, cultural blend' },
  { id: 48, label: "Eclectic Mix", image: "https://ext.same-assets.com/4123950039/2162116070.jpeg", category: 'cultural', description: 'Mixed styles, personal collections, creative combinations' },
  { id: 49, label: "Glam", image: "https://ext.same-assets.com/4123950039/927860819.jpeg", category: 'luxury', description: 'Luxurious materials, metallic accents, sophisticated drama' },
  { id: 50, label: "Retro", image: "https://ext.same-assets.com/4123950039/1246017528.jpeg", category: 'eclectic', description: 'Vintage revival, nostalgic elements, period-specific design' },

  // Additional Modern Styles
  { id: 51, label: "Urban Loft", image: "https://ext.same-assets.com/4123950039/2311013961.jpeg", category: 'modern', description: 'Open spaces, exposed elements, city living aesthetic' },
  { id: 52, label: "Country Cottage", image: "https://ext.same-assets.com/4123950039/690654409.jpeg", category: 'traditional', description: 'Cozy comfort, floral patterns, rural charm' },
  { id: 53, label: "Art Nouveau", image: "https://ext.same-assets.com/4123950039/3217488218.jpeg", category: 'eclectic', description: 'Organic forms, flowing lines, nature-inspired motifs' },
  { id: 54, label: "Bauhaus", image: "https://ext.same-assets.com/4123950039/1390157197.jpeg", category: 'modern', description: 'Functional design, geometric forms, modernist principles' },
  { id: 55, label: "Memphis", image: "https://ext.same-assets.com/4123950039/3540851178.jpeg", category: 'eclectic', description: 'Bold geometry, bright colors, 1980s postmodern style' }
];

const CATEGORY_LABELS = {
  modern: 'Modern',
  traditional: 'Traditional',
  eclectic: 'Eclectic',
  minimalist: 'Minimalist',
  rustic: 'Rustic',
  luxury: 'Luxury',
  eco: 'Eco & Nature',
  cultural: 'Cultural'
};

export const StyleSelector: React.FC<StyleSelectorProps> = ({
  selectedStyles,
  onStyleToggle,
  maxSelection = 5,
  className = ""
}) => {
  const [selectedCategory, setSelectedCategory] = React.useState<string>('all');

  const filteredStyles = selectedCategory === 'all' 
    ? STYLES 
    : STYLES.filter(style => style.category === selectedCategory);

  const isSelected = (styleId: number) => selectedStyles.includes(styleId);
  const canSelect = selectedStyles.length < maxSelection;

  const handleStyleClick = (styleId: number) => {
    if (isSelected(styleId) || canSelect) {
      onStyleToggle(styleId);
    }
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">Choose Design Styles</h3>
        <p className="text-gray-400 text-sm">
          Select up to {maxSelection} styles to blend together ({selectedStyles.length}/{maxSelection} selected)
        </p>
      </div>

      {/* Category Filter */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all border ${
              selectedCategory === 'all'
                ? 'bg-teal-600 text-white border-teal-400'
                : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
            }`}
          >
            All Styles
          </button>
          {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
            <button
              key={key}
              onClick={() => setSelectedCategory(key)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all border ${
                selectedCategory === key
                  ? 'bg-teal-600 text-white border-teal-400'
                  : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Styles Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {filteredStyles.map((style) => {
          const selected = isSelected(style.id);
          const disabled = !selected && !canSelect;

          return (
            <button
              key={style.id}
              onClick={() => handleStyleClick(style.id)}
              disabled={disabled}
              className={`
                group flex flex-col items-stretch justify-end rounded-xl border-2 overflow-hidden bg-gray-900 transition-all h-40 relative shadow-md
                ${selected 
                  ? 'border-teal-500 ring-4 ring-teal-300/40' 
                  : disabled
                    ? 'border-gray-700 bg-gray-900/50 opacity-50 cursor-not-allowed'
                    : 'border-gray-700 hover:border-teal-500 hover:z-10 scale-100 hover:scale-105 active:scale-95 duration-150'
                }
              `}
              title={style.label}
            >
              <img
                src={style.image}
                alt={style.label}
                className="object-cover w-full h-28 group-hover:scale-105 duration-300 rounded-t-xl"
                draggable={false}
              />
              <span
                className={`px-3 py-2 text-sm font-semibold text-center truncate ${
                  selected ? "text-teal-400" : "text-white"
                }`}
              >
                {style.label}
              </span>

              {/* Selection Indicator */}
              {selected && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}

              {/* Selection Number */}
              {selected && (
                <div className="absolute top-2 left-2 w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                  {selectedStyles.indexOf(style.id) + 1}
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Selection Summary */}
      {selectedStyles.length > 0 && (
        <div className="mt-6 p-4 bg-gray-800/30 rounded-xl border border-gray-700">
          <h4 className="text-white font-medium mb-3">Selected Styles ({selectedStyles.length})</h4>
          <div className="flex flex-wrap gap-2">
            {selectedStyles.map((styleId, index) => {
              const style = STYLES.find(s => s.id === styleId);
              if (!style) return null;
              
              return (
                <div key={styleId} className="flex items-center gap-2 bg-teal-500/20 text-teal-300 px-3 py-1 rounded-full text-sm">
                  <span className="w-4 h-4 bg-teal-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    {index + 1}
                  </span>
                  {style.label}
                  <button
                    onClick={() => onStyleToggle(styleId)}
                    className="ml-1 hover:bg-teal-400/20 rounded-full p-0.5"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Selection Limit Warning */}
      {selectedStyles.length >= maxSelection && (
        <div className="mt-4 p-3 bg-amber-500/10 border border-amber-500/30 rounded-lg">
          <div className="text-amber-400 text-sm font-medium">
            Maximum styles selected ({maxSelection})
          </div>
          <div className="text-amber-300 text-xs mt-1">
            Remove a style to select a different one
          </div>
        </div>
      )}
    </div>
  );
};

export default StyleSelector;
export { STYLES, CATEGORY_LABELS };
export type { Style };
