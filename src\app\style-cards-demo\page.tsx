"use client";
import React, { useState } from 'react';
import StyleCardSelector from '../../components/StyleCardSelector';
import { STYLES } from '../../components/StyleSelector';

export default function StyleCardsDemoPage() {
  const [selectedStyles, setSelectedStyles] = useState<number[]>([]);

  const handleStyleToggle = (styleId: number) => {
    setSelectedStyles(prev => 
      prev.includes(styleId)
        ? prev.filter(id => id !== styleId)
        : [...prev, styleId]
    );
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🎨 Style Card Selector
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Experience our new card-based style selector with rich descriptions and beautiful layouts. 
            Each style card includes detailed descriptions from our database schema.
          </p>
        </div>

        {/* Card-based Style Selector */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <StyleCardSelector
            selectedStyles={selectedStyles}
            onStyleToggle={handleStyleToggle}
            maxSelection={5}
          />
        </div>

        {/* Features Comparison */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">Card Design Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <div className="w-12 h-12 bg-teal-500/20 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M4 6h16v2H4zm0 5h16v6c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2v-6zm2 2v3h12v-3H6z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Rich Descriptions</h3>
              <p className="text-gray-400 text-sm">
                Each style includes detailed descriptions from the database schema, helping users understand the aesthetic.
              </p>
            </div>

            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <div className="w-12 h-12 bg-teal-500/20 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Card Layout</h3>
              <p className="text-gray-400 text-sm">
                Large, beautiful cards with prominent images and clear typography for better visual hierarchy.
              </p>
            </div>

            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <div className="w-12 h-12 bg-teal-500/20 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Enhanced Interactions</h3>
              <p className="text-gray-400 text-sm">
                Smooth hover effects, selection indicators, and category badges for better user experience.
              </p>
            </div>

            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <div className="w-12 h-12 bg-teal-500/20 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Visual Feedback</h3>
              <p className="text-gray-400 text-sm">
                Clear selection states with numbered indicators and summary cards for selected styles.
              </p>
            </div>

            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <div className="w-12 h-12 bg-teal-500/20 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Category Organization</h3>
              <p className="text-gray-400 text-sm">
                Styles organized by categories with filtering options and category badges on each card.
              </p>
            </div>

            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <div className="w-12 h-12 bg-teal-500/20 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-teal-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Responsive Design</h3>
              <p className="text-gray-400 text-sm">
                Adaptive grid layout that works perfectly on all screen sizes from mobile to desktop.
              </p>
            </div>
          </div>
        </div>

        {/* Style Statistics */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">Style Collection Statistics</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-teal-400 mb-2">{STYLES.length}</div>
              <div className="text-gray-400 text-sm">Total Styles</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-teal-400 mb-2">8</div>
              <div className="text-gray-400 text-sm">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-teal-400 mb-2">11</div>
              <div className="text-gray-400 text-sm">Eco Styles</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-teal-400 mb-2">20</div>
              <div className="text-gray-400 text-sm">New Additions</div>
            </div>
          </div>
        </div>

        {/* Database Schema Integration */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">Database Schema Integration</h2>
          <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
            <h3 className="text-white font-medium mb-3">✅ All Descriptions Added</h3>
            <p className="text-gray-400 text-sm mb-4">
              Every style now includes the exact description from your database schema:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="bg-gray-900/50 rounded p-3">
                <span className="text-teal-400 font-medium">Minimalist:</span>
                <span className="text-gray-300 ml-2">"Clean lines, neutral colors, uncluttered spaces"</span>
              </div>
              <div className="bg-gray-900/50 rounded p-3">
                <span className="text-teal-400 font-medium">Biophilic:</span>
                <span className="text-gray-300 ml-2">"Nature integration, living plants, organic materials"</span>
              </div>
              <div className="bg-gray-900/50 rounded p-3">
                <span className="text-teal-400 font-medium">Mediterranean:</span>
                <span className="text-gray-300 ml-2">"Warm colors, natural stone, coastal European charm"</span>
              </div>
              <div className="bg-gray-900/50 rounded p-3">
                <span className="text-teal-400 font-medium">Art Nouveau:</span>
                <span className="text-gray-300 ml-2">"Organic forms, flowing lines, nature-inspired motifs"</span>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/design/new"
              className="bg-teal-600 hover:bg-teal-500 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              Try in Design Wizard
            </a>
            <a
              href="/eco-styles-demo"
              className="bg-green-600 hover:bg-green-500 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              🌿 Eco-Design Styles
            </a>
            <a
              href="/components-demo"
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              View All Components
            </a>
            <a
              href="/"
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              Back to Home
            </a>
          </div>
        </div>
      </div>
    </main>
  );
}
