"use client";
import React, { useState } from 'react';
import RoomTypeSelector from '../../components/RoomTypeSelector';
import StyleSelector from '../../components/StyleSelector';
import ColorPaletteSelector from '../../components/ColorPaletteSelector';
import MaterialSelector, { SAMPLE_MATERIALS } from '../../components/MaterialSelector';
import ImageUploader from '../../components/ImageUploader';

export default function ComponentsDemoPage() {
  // State for all components
  const [selectedRoomType, setSelectedRoomType] = useState<string | null>(null);
  const [selectedStyles, setSelectedStyles] = useState<number[]>([]);
  const [selectedPalette, setSelectedPalette] = useState<string | null>(null);
  const [selectedMaterials, setSelectedMaterials] = useState<number[]>([]);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);

  const handleStyleToggle = (styleId: number) => {
    setSelectedStyles(prev => 
      prev.includes(styleId)
        ? prev.filter(id => id !== styleId)
        : [...prev, styleId]
    );
  };

  const handleMaterialToggle = (materialId: number) => {
    setSelectedMaterials(prev => 
      prev.includes(materialId)
        ? prev.filter(id => id !== materialId)
        : [...prev, materialId]
    );
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Design Components Demo
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Explore all the components that power our AI interior design wizard.
            Each component is fully functional and ready for integration.
          </p>
        </div>

        <div className="space-y-16">
          {/* Room Type Selector Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Room Type Selector</h2>
              <p className="text-gray-400">
                Choose from 12 different room types with visual icons and descriptions.
              </p>
            </div>
            <RoomTypeSelector
              selectedRoomType={selectedRoomType}
              onRoomTypeSelect={setSelectedRoomType}
            />
          </section>

          {/* Style Selector Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Style Selector</h2>
              <p className="text-gray-400">
                Multi-select from 35+ design styles with category filtering and visual previews.
              </p>
            </div>
            <StyleSelector
              selectedStyles={selectedStyles}
              onStyleToggle={handleStyleToggle}
              maxSelection={5}
            />
          </section>

          {/* Color Palette Selector Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Color Palette Selector</h2>
              <p className="text-gray-400">
                15 curated color palettes organized by category with live color swatches.
              </p>
            </div>
            <ColorPaletteSelector
              selectedPalette={selectedPalette}
              onPaletteSelect={setSelectedPalette}
            />
          </section>

          {/* Material Selector Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Material Selector</h2>
              <p className="text-gray-400">
                Comprehensive material library with unique icons for each material type.
              </p>
            </div>
            <MaterialSelector
              materials={SAMPLE_MATERIALS}
              selectedMaterials={selectedMaterials}
              onMaterialToggle={handleMaterialToggle}
              maxSelection={5}
            />
          </section>

          {/* Image Uploader Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Image Uploader</h2>
              <p className="text-gray-400">
                Drag-and-drop image upload with validation, preview, and helpful tips.
              </p>
            </div>
            <ImageUploader
              selectedImage={selectedImage}
              onImageSelect={setSelectedImage}
            />
          </section>

          {/* Selection Summary */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Current Selections</h2>
              <p className="text-gray-400">
                Summary of all your current selections across components.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Room Type</h3>
                <p className="text-gray-400 text-sm">
                  {selectedRoomType || 'None selected'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Styles</h3>
                <p className="text-gray-400 text-sm">
                  {selectedStyles.length > 0 ? `${selectedStyles.length} selected` : 'None selected'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Color Palette</h3>
                <p className="text-gray-400 text-sm">
                  {selectedPalette || 'None selected'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Materials</h3>
                <p className="text-gray-400 text-sm">
                  {selectedMaterials.length > 0 ? `${selectedMaterials.length} selected` : 'None selected'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Image</h3>
                <p className="text-gray-400 text-sm">
                  {selectedImage ? selectedImage.name : 'None uploaded'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Ready to Generate</h3>
                <p className="text-gray-400 text-sm">
                  {selectedRoomType && selectedStyles.length > 0 && selectedPalette && selectedMaterials.length > 0
                    ? '✅ All set!' 
                    : '⏳ Select more options'
                  }
                </p>
              </div>
            </div>
          </section>

          {/* Navigation */}
          <section className="text-center">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/design/new"
                className="bg-teal-600 hover:bg-teal-500 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                Try the Full Design Wizard
              </a>
              <a
                href="/materials-demo"
                className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                View Materials Demo
              </a>
              <a
                href="/"
                className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                Back to Home
              </a>
            </div>
          </section>
        </div>
      </div>
    </main>
  );
}
