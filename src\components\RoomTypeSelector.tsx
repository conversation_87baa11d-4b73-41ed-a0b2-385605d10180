"use client";
import React from 'react';

interface RoomType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  preview_image?: string;
}

interface RoomTypeSelectorProps {
  selectedRoomType: string | null;
  onRoomTypeSelect: (roomTypeId: string) => void;
  className?: string;
}

const ROOM_TYPES: RoomType[] = [
  {
    id: 'living-room',
    name: 'Living Room',
    description: 'Main living and entertainment space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M4 6h16v2H4zm0 5h16v6c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2v-6zm2 2v3h12v-3H6z"/>
        <path d="M2 4h20v2H2zm2 15h16v2H4z"/>
      </svg>
    )
  },
  {
    id: 'bedroom',
    name: 'Bedroom',
    description: 'Sleeping and personal space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M7 14c1.66 0 3-1.34 3-3S8.66 8 7 8s-3 1.34-3 3 1.34 3 3 3zm0-4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"/>
        <path d="M20 9V6c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v3c-1.1 0-2 .9-2 2v4h2c0 1.1.9 2 2 2s2-.9 2-2h8c0 1.1.9 2 2 2s2-.9 2-2h2v-4c0-1.1-.9-2-2-2zM6 6h12v3H6V6z"/>
      </svg>
    )
  },
  {
    id: 'kitchen',
    name: 'Kitchen',
    description: 'Cooking and dining preparation area',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M18 2.01L6 2c-1.1 0-2 .89-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.11-.9-1.99-2-1.99zM18 20H6V4h2v7l2.5-1.5L13 11V4h5v16z"/>
        <path d="M8 6h2v2H8zm0 3h2v2H8z"/>
      </svg>
    )
  },
  {
    id: 'bathroom',
    name: 'Bathroom',
    description: 'Personal hygiene and bathing space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
        <circle cx="12" cy="17" r="1.5"/>
      </svg>
    )
  },
  {
    id: 'dining-room',
    name: 'Dining Room',
    description: 'Formal dining and entertaining space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M22 9c0-1.1-.9-2-2-2V4c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v3c-1.1 0-2 .9-2 2v9h2.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5H15c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5H20V9zM6 4h12v3H6V4zm0 5h12v2H6V9z"/>
        <circle cx="9" cy="12" r="1"/>
        <circle cx="15" cy="12" r="1"/>
      </svg>
    )
  },
  {
    id: 'home-office',
    name: 'Home Office',
    description: 'Work and study space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M20 3H4c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h3l-1 1v1h12v-1l-1-1h3c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 13H4V5h16v11z"/>
        <path d="M6 7h8v2H6zm0 3h8v2H6zm0 3h5v2H6z"/>
      </svg>
    )
  },
  {
    id: 'kids-room',
    name: 'Kids Room',
    description: 'Children\'s bedroom and play area',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        <circle cx="9" cy="9" r="1.5"/>
        <circle cx="15" cy="9" r="1.5"/>
        <path d="M8 13h8c0 2.21-1.79 4-4 4s-4-1.79-4-4z"/>
      </svg>
    )
  },
  {
    id: 'media-room',
    name: 'Media Room',
    description: 'Entertainment and media consumption space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M21 3H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h5l-1 1v1h8v-1l-1-1h5c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 12H3V5h18v10z"/>
        <path d="M9 8v8l7-4z"/>
      </svg>
    )
  },
  {
    id: 'mudroom',
    name: 'Mudroom',
    description: 'Entry and storage space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
      </svg>
    )
  },
  {
    id: 'patio',
    name: 'Patio',
    description: 'Outdoor living and entertainment space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        <path d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z"/>
      </svg>
    )
  },
  {
    id: 'laundry-room',
    name: 'Laundry Room',
    description: 'Washing and utility space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M9.17 16.83L12 14l2.83 2.83 1.41-1.41L12 11.17l-4.24 4.24 1.41 1.42z"/>
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
        <circle cx="7" cy="7" r="1.5"/>
        <circle cx="17" cy="7" r="1.5"/>
      </svg>
    )
  },
  {
    id: 'home-gym',
    name: 'Home Gym',
    description: 'Exercise and fitness space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29l-1.43-1.43z"/>
        <circle cx="12" cy="12" r="3"/>
      </svg>
    )
  },
  {
    id: 'guest-room',
    name: 'Guest Room',
    description: 'Temporary accommodation space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M7 14c1.66 0 3-1.34 3-3S8.66 8 7 8s-3 1.34-3 3 1.34 3 3 3zm0-4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"/>
        <path d="M20 9V6c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v3c-1.1 0-2 .9-2 2v4h2c0 1.1.9 2 2 2s2-.9 2-2h8c0 1.1.9 2 2 2s2-.9 2-2h2v-4c0-1.1-.9-2-2-2zM6 6h12v3H6V6z"/>
        <path d="M17 8h2v2h-2z"/>
      </svg>
    )
  },
  {
    id: 'walk-in-closet',
    name: 'Walk-in Closet',
    description: 'Clothing storage and dressing area',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        <path d="M6 2v6h.01L6 8.01 10 12l-4 4 .01.01H6V22h5v-7.5l4-4 4 4V22h5v-5.99h-.01L24 16l-4-4 4-3.99-.01-.01H24V2h-5v5.5L12 11 5.5 7.5V2H6z"/>
      </svg>
    )
  },
  {
    id: 'basement',
    name: 'Basement',
    description: 'Lower level multipurpose space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2L2 12h3v8h14v-8h3L12 2zm2 14h-4v-4h4v4z"/>
        <path d="M7 14h2v2H7zm8 0h2v2h-2z"/>
      </svg>
    )
  },
  {
    id: 'attic',
    name: 'Attic',
    description: 'Upper level storage or living space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 3L2 12h3v8h6v-6h2v6h6v-8h3L12 3z"/>
        <path d="M7 16h2v2H7zm8 0h2v2h-2z"/>
      </svg>
    )
  },
  {
    id: 'sunroom',
    name: 'Sunroom',
    description: 'Glass-enclosed seasonal living space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z"/>
        <path d="M5 16h14v4H5z"/>
      </svg>
    )
  },
  {
    id: 'library',
    name: 'Library',
    description: 'Reading and book storage space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
      </svg>
    )
  },
  {
    id: 'game-room',
    name: 'Game Room',
    description: 'Recreation and entertainment space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
        <circle cx="12" cy="12" r="2"/>
      </svg>
    )
  },
  {
    id: 'wine-cellar',
    name: 'Wine Cellar',
    description: 'Wine storage and tasting area',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M6 3l2 6v8c0 1.1.9 2 2 2h4c1.1 0 2-.9 2-2V9l2-6H6zm5 7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm2 0c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/>
        <path d="M9 2h6v1H9z"/>
      </svg>
    )
  },
  {
    id: 'nursery',
    name: 'Nursery',
    description: 'Baby and infant care space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        <circle cx="9" cy="9" r="1"/>
        <circle cx="15" cy="9" r="1"/>
        <path d="M12 17.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"/>
      </svg>
    )
  },
  {
    id: 'pantry',
    name: 'Pantry',
    description: 'Food storage and organization area',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
        <path d="M9 12h6v2H9zm0 3h6v2H9z"/>
      </svg>
    )
  },
  {
    id: 'foyer',
    name: 'Foyer',
    description: 'Entry and welcome space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2L2 12h3v8h6v-6h2v6h6v-8h3L12 2z"/>
        <path d="M12 7.69l-6 6V20h4v-6h4v6h4v-6.31l-6-6z"/>
      </svg>
    )
  },
  {
    id: 'hallway',
    name: 'Hallway',
    description: 'Connecting passage space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M13 3h-2v18h2V3z"/>
        <path d="M3 13h18v-2H3v2z"/>
        <path d="M5 5h2v2H5zm12 0h2v2h-2zM5 17h2v2H5zm12 0h2v2h-2z"/>
      </svg>
    )
  },
  {
    id: 'balcony',
    name: 'Balcony',
    description: 'Outdoor extension space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z"/>
        <rect x="2" y="18" width="20" height="2"/>
      </svg>
    )
  },
  {
    id: 'garage',
    name: 'Garage',
    description: 'Vehicle storage and workshop space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M5 11l1.5-4.5h11L19 11v5c0 .55-.45 1-1 1s-1-.45-1-1v-1H7v1c0 .55-.45 1-1 1s-1-.45-1-1v-5zM6.5 13.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S5 11.17 5 12s.67 1.5 1.5 1.5zm11 0c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5-1.5.67-1.5 1.5.67 1.5 1.5 1.5z"/>
        <path d="M2 19h20v2H2z"/>
      </svg>
    )
  }
];

export const RoomTypeSelector: React.FC<RoomTypeSelectorProps> = ({
  selectedRoomType,
  onRoomTypeSelect,
  className = ""
}) => {
  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">Choose Room Type</h3>
        <p className="text-gray-400 text-sm">
          Select the type of room you want to design
        </p>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
        {ROOM_TYPES.map((roomType) => {
          const isSelected = selectedRoomType === roomType.id;
          
          return (
            <button
              key={roomType.id}
              onClick={() => onRoomTypeSelect(roomType.id)}
              className={`
                group flex flex-col items-center p-3 rounded-xl border-2 transition-all duration-200 relative
                ${isSelected
                  ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40'
                  : 'border-gray-700 bg-gray-900 hover:border-teal-500 hover:bg-gray-800 active:scale-95'
                }
              `}
              title={roomType.description}
            >
              {/* Room Icon */}
              <div className={`mb-3 transition-all duration-200 ${
                isSelected
                  ? 'scale-110 text-teal-400 drop-shadow-lg'
                  : 'text-gray-400 group-hover:text-teal-400 group-hover:scale-105 group-hover:drop-shadow-[0_0_8px_rgba(20,184,166,0.6)]'
              }`}>
                {roomType.icon}
              </div>

              {/* Room Name */}
              <span className={`text-xs font-medium text-center leading-tight ${
                isSelected ? 'text-teal-400' : 'text-white'
              }`}>
                {roomType.name}
              </span>

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Selected Room Info */}
      {selectedRoomType && (
        <div className="mt-6 p-4 bg-gray-800/30 rounded-xl border border-gray-700">
          {(() => {
            const selectedRoom = ROOM_TYPES.find(room => room.id === selectedRoomType);
            if (!selectedRoom) return null;
            
            return (
              <div className="flex items-center gap-4">
                <div className="text-teal-400 w-8 h-8 flex items-center justify-center">
                  {selectedRoom.icon}
                </div>
                <div>
                  <h4 className="text-white font-medium">{selectedRoom.name}</h4>
                  <p className="text-gray-400 text-sm">{selectedRoom.description}</p>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default RoomTypeSelector;
export { ROOM_TYPES };
export type { RoomType };
