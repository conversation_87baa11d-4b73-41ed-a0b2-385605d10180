"use client";
import React from 'react';

interface RoomType {
  id: string;
  name: string;
  description: string;
  icon: string;
  preview_image?: string;
}

interface RoomTypeSelectorProps {
  selectedRoomType: string | null;
  onRoomTypeSelect: (roomTypeId: string) => void;
  className?: string;
}

const ROOM_TYPES: RoomType[] = [
  {
    id: 'living-room',
    name: 'Living Room',
    description: 'Main gathering space for relaxation and entertainment',
    icon: '🛋️',
    preview_image: 'https://ext.same-assets.com/4123950039/3970137075.jpeg'
  },
  {
    id: 'bedroom',
    name: 'Bedroom',
    description: 'Personal retreat for rest and relaxation',
    icon: '🛏️',
    preview_image: 'https://ext.same-assets.com/4123950039/2719286588.jpeg'
  },
  {
    id: 'kitchen',
    name: 'Kitchen',
    description: 'Heart of the home for cooking and dining',
    icon: '🍳',
    preview_image: 'https://ext.same-assets.com/4123950039/1267053207.jpeg'
  },
  {
    id: 'bathroom',
    name: 'Bathroom',
    description: 'Private space for personal care and wellness',
    icon: '🛁',
    preview_image: 'https://ext.same-assets.com/4123950039/4031576135.jpeg'
  },
  {
    id: 'dining-room',
    name: 'Dining Room',
    description: 'Formal space for meals and gatherings',
    icon: '🍽️',
    preview_image: 'https://ext.same-assets.com/4123950039/2311013961.jpeg'
  },
  {
    id: 'home-office',
    name: 'Home Office',
    description: 'Productive workspace for work and study',
    icon: '💻',
    preview_image: 'https://ext.same-assets.com/4123950039/4247774748.jpeg'
  },
  {
    id: 'kids-room',
    name: "Kids' Room",
    description: 'Playful and safe space for children',
    icon: '🧸',
    preview_image: 'https://ext.same-assets.com/4123950039/2162116070.jpeg'
  },
  {
    id: 'media-room',
    name: 'Media Room',
    description: 'Entertainment space for movies and gaming',
    icon: '📺',
    preview_image: 'https://ext.same-assets.com/4123950039/3217488218.jpeg'
  },
  {
    id: 'mudroom',
    name: 'Mudroom',
    description: 'Transitional space for storage and organization',
    icon: '👢',
    preview_image: 'https://ext.same-assets.com/4123950039/4253859114.jpeg'
  },
  {
    id: 'patio',
    name: 'Patio',
    description: 'Outdoor living space for relaxation',
    icon: '🌿',
    preview_image: 'https://ext.same-assets.com/4123950039/2462314040.jpeg'
  },
  {
    id: 'laundry-room',
    name: 'Laundry Room',
    description: 'Functional space for cleaning and storage',
    icon: '🧺',
    preview_image: 'https://ext.same-assets.com/4123950039/362001557.jpeg'
  },
  {
    id: 'home-gym',
    name: 'Home Gym',
    description: 'Dedicated space for fitness and wellness',
    icon: '🏋️',
    preview_image: 'https://ext.same-assets.com/4123950039/690654409.jpeg'
  }
];

export const RoomTypeSelector: React.FC<RoomTypeSelectorProps> = ({
  selectedRoomType,
  onRoomTypeSelect,
  className = ""
}) => {
  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">Choose Room Type</h3>
        <p className="text-gray-400 text-sm">
          Select the type of room you want to design
        </p>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {ROOM_TYPES.map((roomType) => {
          const isSelected = selectedRoomType === roomType.id;
          
          return (
            <button
              key={roomType.id}
              onClick={() => onRoomTypeSelect(roomType.id)}
              className={`
                group flex flex-col items-center p-4 rounded-xl border-2 transition-all duration-200 relative
                ${isSelected 
                  ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40' 
                  : 'border-gray-700 bg-gray-900 hover:border-teal-500 hover:bg-gray-800 active:scale-95'
                }
              `}
              title={roomType.description}
            >
              {/* Room Icon */}
              <div className={`text-3xl mb-2 transition-transform duration-200 ${
                isSelected ? 'scale-110' : 'group-hover:scale-105'
              }`}>
                {roomType.icon}
              </div>

              {/* Room Name */}
              <span className={`text-sm font-medium text-center leading-tight ${
                isSelected ? 'text-teal-400' : 'text-white'
              }`}>
                {roomType.name}
              </span>

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Selected Room Info */}
      {selectedRoomType && (
        <div className="mt-6 p-4 bg-gray-800/30 rounded-xl border border-gray-700">
          {(() => {
            const selectedRoom = ROOM_TYPES.find(room => room.id === selectedRoomType);
            if (!selectedRoom) return null;
            
            return (
              <div className="flex items-center gap-4">
                <div className="text-2xl">{selectedRoom.icon}</div>
                <div>
                  <h4 className="text-white font-medium">{selectedRoom.name}</h4>
                  <p className="text-gray-400 text-sm">{selectedRoom.description}</p>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default RoomTypeSelector;
export { ROOM_TYPES };
export type { RoomType };
