"use client";
import React from 'react';

interface RoomType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  preview_image?: string;
}

interface RoomTypeSelectorProps {
  selectedRoomType: string | null;
  onRoomTypeSelect: (roomTypeId: string) => void;
  className?: string;
}

const ROOM_TYPES: RoomType[] = [
  {
    id: 'living-room',
    name: 'Living Room',
    description: 'Main living and entertainment space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 7h18v10c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V7z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 7V5c0-1.1.9-2 2-2h8c1.1 0 2 .9 2 2v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 11h4v4H7z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M13 13h4v2h-4z"/>
      </svg>
    )
  },
  {
    id: 'bedroom',
    name: 'Bedroom',
    description: 'Sleeping and personal space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 12h18v6c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-6z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12V9c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v3"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 7c0-1.1.9-2 2-2s2 .9 2 2"/>
        <circle cx="6" cy="18" r="1"/>
        <circle cx="18" cy="18" r="1"/>
      </svg>
    )
  },
  {
    id: 'kitchen',
    name: 'Kitchen',
    description: 'Cooking and dining preparation area',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="3" width="18" height="18" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 3v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M17 3v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 9h18"/>
        <circle cx="8" cy="14" r="1"/>
        <circle cx="12" cy="14" r="1"/>
        <circle cx="16" cy="14" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 18h8"/>
      </svg>
    )
  },
  {
    id: 'bathroom',
    name: 'Bathroom',
    description: 'Personal hygiene and bathing space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 12h16v6c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2v-6z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 12V8c0-2.2 1.8-4 4-4h4c2.2 0 4 1.8 4 4v4"/>
        <circle cx="8" cy="8" r="1"/>
        <circle cx="16" cy="8" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 16v2"/>
      </svg>
    )
  },
  {
    id: 'dining-room',
    name: 'Dining Room',
    description: 'Formal dining and entertaining space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <ellipse cx="12" cy="12" rx="8" ry="4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 10v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 10v4"/>
        <circle cx="8" cy="12" r="1"/>
        <circle cx="12" cy="12" r="1"/>
        <circle cx="16" cy="12" r="1"/>
      </svg>
    )
  },
  {
    id: 'home-office',
    name: 'Home Office',
    description: 'Work and study space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="2" y="4" width="20" height="12" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 20h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 16v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 8h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 11h6"/>
      </svg>
    )
  },
  {
    id: 'kids-room',
    name: 'Kids Room',
    description: 'Children\'s bedroom and play area',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 12h18v6c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-6z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12V9c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v3"/>
        <circle cx="8" cy="8" r="2"/>
        <circle cx="16" cy="8" r="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M10 15h4"/>
        <circle cx="6" cy="18" r="1"/>
        <circle cx="18" cy="18" r="1"/>
      </svg>
    )
  },
  {
    id: 'media-room',
    name: 'Media Room',
    description: 'Entertainment and media consumption space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="2" y="4" width="20" height="12" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 20h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 16v4"/>
        <polygon points="10,8 10,14 16,11"/>
      </svg>
    )
  },
  {
    id: 'mudroom',
    name: 'Mudroom',
    description: 'Entry and storage space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="8" width="18" height="12" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 8V6c0-1.1.9-2 2-2h6c1.1 0 2 .9 2 2v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 12v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 12v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 12v4"/>
      </svg>
    )
  },
  {
    id: 'patio',
    name: 'Patio',
    description: 'Outdoor living and entertainment space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 2v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 20v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.93 4.93l1.41 1.41"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M17.66 17.66l1.41 1.41"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M2 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M20 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6.34 17.66l-1.41 1.41"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M19.07 4.93l-1.41 1.41"/>
      </svg>
    )
  },
  {
    id: 'laundry-room',
    name: 'Laundry Room',
    description: 'Washing and utility space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="4" y="4" width="16" height="16" rx="2"/>
        <circle cx="8" cy="8" r="1"/>
        <circle cx="12" cy="8" r="1"/>
        <circle cx="12" cy="14" r="4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M10 14c0-1.1.9-2 2-2s2 .9 2 2"/>
      </svg>
    )
  },
  {
    id: 'home-gym',
    name: 'Home Gym',
    description: 'Exercise and fitness space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 12h10"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 8v8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M17 8v8"/>
        <circle cx="5" cy="12" r="2"/>
        <circle cx="19" cy="12" r="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 10h6v4H9z"/>
      </svg>
    )
  },
  {
    id: 'guest-room',
    name: 'Guest Room',
    description: 'Temporary accommodation space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 12h18v6c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-6z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12V9c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v3"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 7c0-1.1.9-2 2-2s2 .9 2 2"/>
        <circle cx="6" cy="18" r="1"/>
        <circle cx="18" cy="18" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 8h2v2h-2z"/>
      </svg>
    )
  },
  {
    id: 'walk-in-closet',
    name: 'Walk-in Closet',
    description: 'Clothing storage and dressing area',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="3" width="18" height="18" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 3v18"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 3v18"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 8h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 16h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 8h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 16h2"/>
        <circle cx="12" cy="15" r="1"/>
      </svg>
    )
  },
  {
    id: 'basement',
    name: 'Basement',
    description: 'Lower level multipurpose space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 3L3 12h2v8h14v-8h2L12 3z"/>
        <rect x="8" y="14" width="8" height="4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 20h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 14h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 14h2"/>
      </svg>
    )
  },
  {
    id: 'attic',
    name: 'Attic',
    description: 'Upper level storage or living space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 3L3 12h2v8h6v-6h2v6h6v-8h2L12 3z"/>
        <rect x="8" y="8" width="8" height="4"/>
        <circle cx="10" cy="10" r="1"/>
        <circle cx="14" cy="10" r="1"/>
      </svg>
    )
  },
  {
    id: 'sunroom',
    name: 'Sunroom',
    description: 'Glass-enclosed seasonal living space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="8" width="18" height="12" rx="2"/>
        <circle cx="12" cy="5" r="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 1v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.22 4.22l1.42 1.42"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M1 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M18.36 5.64l1.42-1.42"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 14h10"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 17h6"/>
      </svg>
    )
  },
  {
    id: 'library',
    name: 'Library',
    description: 'Reading and book storage space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="4" width="18" height="16" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 4v16"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M11 4v16"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 4v16"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M19 4v16"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 8h1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M13 8h1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M17 8h1"/>
      </svg>
    )
  },
  {
    id: 'game-room',
    name: 'Game Room',
    description: 'Recreation and entertainment space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M15 7.5V2H9v5.5l3 3 3-3zM7.5 9H2v6h5.5l3-3-3-3zM9 16.5V22h6v-5.5l-3-3-3 3zM16.5 9l-3 3 3 3H22V9h-5.5z"/>
        <circle cx="12" cy="12" r="2"/>
      </svg>
    )
  },
  {
    id: 'wine-cellar',
    name: 'Wine Cellar',
    description: 'Wine storage and tasting area',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M6 3l2 6v8c0 1.1.9 2 2 2h4c1.1 0 2-.9 2-2V9l2-6H6zm5 7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm2 0c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/>
        <path d="M9 2h6v1H9z"/>
      </svg>
    )
  },
  {
    id: 'nursery',
    name: 'Nursery',
    description: 'Baby and infant care space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        <circle cx="9" cy="9" r="1"/>
        <circle cx="15" cy="9" r="1"/>
        <path d="M12 17.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"/>
      </svg>
    )
  },
  {
    id: 'pantry',
    name: 'Pantry',
    description: 'Food storage and organization area',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
        <path d="M9 12h6v2H9zm0 3h6v2H9z"/>
      </svg>
    )
  },
  {
    id: 'foyer',
    name: 'Foyer',
    description: 'Entry and welcome space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2L2 12h3v8h6v-6h2v6h6v-8h3L12 2z"/>
        <path d="M12 7.69l-6 6V20h4v-6h4v6h4v-6.31l-6-6z"/>
      </svg>
    )
  },
  {
    id: 'hallway',
    name: 'Hallway',
    description: 'Connecting passage space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M13 3h-2v18h2V3z"/>
        <path d="M3 13h18v-2H3v2z"/>
        <path d="M5 5h2v2H5zm12 0h2v2h-2zM5 17h2v2H5zm12 0h2v2h-2z"/>
      </svg>
    )
  },
  {
    id: 'balcony',
    name: 'Balcony',
    description: 'Outdoor extension space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z"/>
        <rect x="2" y="18" width="20" height="2"/>
      </svg>
    )
  },
  {
    id: 'garage',
    name: 'Garage',
    description: 'Vehicle storage and workshop space',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M5 11l1.5-4.5h11L19 11v5c0 .55-.45 1-1 1s-1-.45-1-1v-1H7v1c0 .55-.45 1-1 1s-1-.45-1-1v-5zM6.5 13.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S5 11.17 5 12s.67 1.5 1.5 1.5zm11 0c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5-1.5.67-1.5 1.5.67 1.5 1.5 1.5z"/>
        <path d="M2 19h20v2H2z"/>
      </svg>
    )
  }
];

export const RoomTypeSelector: React.FC<RoomTypeSelectorProps> = ({
  selectedRoomType,
  onRoomTypeSelect,
  className = ""
}) => {
  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">Choose Room Type</h3>
        <p className="text-gray-400 text-sm">
          Select the type of room you want to design
        </p>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
        {ROOM_TYPES.map((roomType) => {
          const isSelected = selectedRoomType === roomType.id;
          
          return (
            <button
              key={roomType.id}
              onClick={() => onRoomTypeSelect(roomType.id)}
              className={`
                group flex flex-col items-center p-3 rounded-xl border-2 transition-all duration-200 relative
                ${isSelected
                  ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40'
                  : 'border-gray-700 bg-gray-900 hover:border-teal-500 hover:bg-gray-800 active:scale-95'
                }
              `}
              title={roomType.description}
            >
              {/* Room Icon */}
              <div className={`mb-3 transition-all duration-200 ${
                isSelected
                  ? 'scale-110 text-teal-400 drop-shadow-lg'
                  : 'text-gray-400 group-hover:text-teal-400 group-hover:scale-105 group-hover:drop-shadow-[0_0_8px_rgba(20,184,166,0.6)]'
              }`}>
                {roomType.icon}
              </div>

              {/* Room Name */}
              <span className={`text-xs font-medium text-center leading-tight ${
                isSelected ? 'text-teal-400' : 'text-white'
              }`}>
                {roomType.name}
              </span>

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Selected Room Info */}
      {selectedRoomType && (
        <div className="mt-6 p-4 bg-gray-800/30 rounded-xl border border-gray-700">
          {(() => {
            const selectedRoom = ROOM_TYPES.find(room => room.id === selectedRoomType);
            if (!selectedRoom) return null;
            
            return (
              <div className="flex items-center gap-4">
                <div className="text-teal-400 w-8 h-8 flex items-center justify-center">
                  {selectedRoom.icon}
                </div>
                <div>
                  <h4 className="text-white font-medium">{selectedRoom.name}</h4>
                  <p className="text-gray-400 text-sm">{selectedRoom.description}</p>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default RoomTypeSelector;
export { ROOM_TYPES };
export type { RoomType };
