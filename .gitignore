# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
/ss
/api.md
/database-schema.sql

/grok_roadmap_explained.md
/ecodesign_phase1.md
/ecodesign_phase2.md
/ecodesign_phase3.md
/ecodesign_phase4.md
/final_destination.md
/grock_roadmap.md
/grock-ai-prompt.md
/interior-ai-prompt.md
/roomai_lovable.md
/roomy-ai.md